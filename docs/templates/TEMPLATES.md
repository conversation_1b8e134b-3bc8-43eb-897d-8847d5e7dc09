# ⚙️ Configuration Templates & Package Setup

## 📦 Root Package.json (Monorepo)

```json
{
  "name": "enterprise-platform",
  "version": "1.0.0",
  "description": "Enterprise-grade software platform with microservices architecture",
  "private": true,
  "workspaces": ["apps/*", "services/*", "libs/*"],
  "scripts": {
    "build": "lerna run build",
    "test": "lerna run test",
    "test:unit": "lerna run test:unit",
    "test:integration": "lerna run test:integration",
    "test:e2e": "lerna run test:e2e",
    "test:coverage": "lerna run test:coverage",
    "lint": "lerna run lint",
    "lint:fix": "lerna run lint:fix",
    "type-check": "lerna run type-check",
    "clean": "lerna run clean",
    "dev": "lerna run dev --parallel",
    "start": "lerna run start",
    "docker:build": "docker-compose build",
    "docker:up": "docker-compose up -d",
    "docker:down": "docker-compose down",
    "k8s:deploy": "kubectl apply -f infrastructure/kubernetes/",
    "migration:run": "lerna run migration:run",
    "migration:generate": "lerna run migration:generate",
    "seed": "lerna run seed",
    "security:audit": "npm audit --audit-level high",
    "security:scan": "snyk test",
    "performance:test": "k6 run tests/performance/load-test.js",
    "docs:generate": "typedoc --options typedoc.json",
    "release": "lerna version && lerna publish"
  },
  "devDependencies": {
    "@lerna/legacy-core": "^7.0.0",
    "@types/node": "^18.15.0",
    "@typescript-eslint/eslint-plugin": "^5.57.0",
    "@typescript-eslint/parser": "^5.57.0",
    "concurrently": "^8.0.1",
    "eslint": "^8.37.0",
    "eslint-config-prettier": "^8.8.0",
    "eslint-plugin-import": "^2.27.5",
    "eslint-plugin-security": "^1.7.1",
    "husky": "^8.0.3",
    "lerna": "^7.0.0",
    "lint-staged": "^13.2.0",
    "prettier": "^2.8.7",
    "rimraf": "^5.0.0",
    "typedoc": "^0.24.1",
    "typescript": "^5.0.2"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/your-org/enterprise-platform.git"
  },
  "author": "Your Organization",
  "license": "MIT",
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "pre-push": "npm run test:unit",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": ["eslint --fix", "prettier --write", "git add"],
    "*.{json,md}": ["prettier --write", "git add"]
  }
}
```

## 📋 Service Package.json Template

```json
{
  "name": "@enterprise-platform/user-service",
  "version": "1.0.0",
  "description": "User management microservice",
  "main": "dist/main.js",
  "scripts": {
    "prebuild": "rimraf dist",
    "build": "nest build",
    "build:watch": "nest build --watch",
    "start": "node dist/main",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:unit": "jest --testPathPattern=unit",
    "test:integration": "jest --testPathPattern=integration",
    "test:e2e": "jest --config ./test/jest-e2e.json",
    "test:coverage": "jest --coverage",
    "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
    "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"",
    "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix",
    "type-check": "tsc --noEmit",
    "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"",
    "migration:generate": "typeorm migration:generate -d src/config/database.ts",
    "migration:run": "typeorm migration:run -d src/config/database.ts",
    "migration:revert": "typeorm migration:revert -d src/config/database.ts",
    "seed": "ts-node -r tsconfig-paths/register src/database/seeds/run-seeds.ts",
    "docker:build": "docker build -t user-service .",
    "docker:run": "docker run -p 3000:3000 user-service",
    "clean": "rimraf dist node_modules coverage"
  },
  "dependencies": {
    "@nestjs/common": "^10.0.0",
    "@nestjs/core": "^10.0.0",
    "@nestjs/platform-express": "^10.0.0",
    "@nestjs/platform-fastify": "^10.0.0",
    "@nestjs/typeorm": "^10.0.0",
    "@nestjs/cqrs": "^10.0.0",
    "@nestjs/jwt": "^10.0.0",
    "@nestjs/passport": "^10.0.0",
    "@nestjs/swagger": "^7.0.0",
    "@nestjs/throttler": "^4.0.0",
    "@nestjs/config": "^3.0.0",
    "typeorm": "^0.3.17",
    "pg": "^8.11.0",
    "redis": "^4.6.0",
    "bcrypt": "^5.1.0",
    "jsonwebtoken": "^9.0.0",
    "class-validator": "^0.14.0",
    "class-transformer": "^0.5.1",
    "helmet": "^7.0.0",
    "express-rate-limit": "^6.7.0",
    "compression": "^1.7.4",
    "uuid": "^9.0.0",
    "joi": "^17.9.0",
    "rxjs": "^7.8.0",
    "reflect-metadata": "^0.1.13"
  },
  "devDependencies": {
    "@nestjs/cli": "^10.0.0",
    "@nestjs/schematics": "^10.0.0",
    "@nestjs/testing": "^10.0.0",
    "@types/express": "^4.17.17",
    "@types/jest": "^29.5.0",
    "@types/node": "^18.15.0",
    "@types/supertest": "^2.0.12",
    "@types/bcrypt": "^5.0.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/uuid": "^9.0.0",
    "@types/compression": "^1.7.2",
    "@typescript-eslint/eslint-plugin": "^5.57.0",
    "@typescript-eslint/parser": "^5.57.0",
    "eslint": "^8.37.0",
    "eslint-config-prettier": "^8.8.0",
    "eslint-plugin-prettier": "^4.2.1",
    "jest": "^29.5.0",
    "prettier": "^2.8.7",
    "source-map-support": "^0.5.21",
    "supertest": "^6.3.3",
    "ts-jest": "^29.1.0",
    "ts-loader": "^9.4.2",
    "ts-node": "^10.9.1",
    "tsconfig-paths": "^4.2.0",
    "typescript": "^5.0.2"
  },
  "jest": {
    "moduleFileExtensions": ["js", "json", "ts"],
    "rootDir": "src",
    "testRegex": ".*\\.spec\\.ts$",
    "transform": {
      "^.+\\.(t|j)s$": "ts-jest"
    },
    "collectCoverageFrom": [
      "**/*.(t|j)s",
      "!**/*.spec.ts",
      "!**/node_modules/**"
    ],
    "coverageDirectory": "../coverage",
    "testEnvironment": "node",
    "coverageThreshold": {
      "global": {
        "branches": 80,
        "functions": 80,
        "lines": 80,
        "statements": 80
      }
    }
  }
}
```

## 🔧 TypeScript Configuration

```json
// tsconfig.json (Root)
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022"],
    "module": "commonjs",
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "paths": {
      "@enterprise-platform/*": ["libs/*/src"],
      "@shared/*": ["libs/shared-types/src/*"],
      "@domain/*": ["libs/domain-models/src/*"],
      "@utils/*": ["libs/common-utils/src/*"],
      "@security/*": ["libs/security/src/*"],
      "@algorithms/*": ["libs/algorithms/src/*"]
    },
    "incremental": true,
    "removeComments": true
  },
  "include": ["apps/**/*", "services/**/*", "libs/**/*"],
  "exclude": [
    "node_modules",
    "dist",
    "coverage",
    "**/*.spec.ts",
    "**/*.test.ts"
  ]
}
```

```json
// services/user-service/tsconfig.json
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src",
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "@domain/*": ["src/domain/*"],
      "@application/*": ["src/application/*"],
      "@infrastructure/*": ["src/infrastructure/*"],
      "@interface/*": ["src/interface/*"],
      "@shared/*": ["../../libs/shared-types/src/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]
}
```

## 🔍 ESLint Configuration

```javascript
// .eslintrc.js (Root)
module.exports = {
  parser: "@typescript-eslint/parser",
  parserOptions: {
    project: "tsconfig.json",
    tsconfigRootDir: __dirname,
    sourceType: "module",
  },
  plugins: ["@typescript-eslint/eslint-plugin", "import", "security"],
  extends: [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "@typescript-eslint/recommended-requiring-type-checking",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "plugin:security/recommended",
    "prettier",
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: [".eslintrc.js", "dist/", "node_modules/", "coverage/"],
  rules: {
    // TypeScript
    "@typescript-eslint/interface-name-prefix": "off",
    "@typescript-eslint/explicit-function-return-type": "error",
    "@typescript-eslint/explicit-module-boundary-types": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/prefer-nullish-coalescing": "error",
    "@typescript-eslint/prefer-optional-chain": "error",
    "@typescript-eslint/no-floating-promises": "error",
    "@typescript-eslint/await-thenable": "error",
    "@typescript-eslint/no-misused-promises": "error",

    // Import rules
    "import/order": [
      "error",
      {
        groups: [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index",
        ],
        "newlines-between": "always",
        alphabetize: {
          order: "asc",
          caseInsensitive: true,
        },
      },
    ],
    "import/no-unresolved": "error",
    "import/no-cycle": "error",

    // General
    "no-console": "warn",
    "no-debugger": "error",
    "prefer-const": "error",
    "no-var": "error",
    eqeqeq: "error",
    curly: "error",

    // Security
    "security/detect-object-injection": "error",
    "security/detect-non-literal-fs-filename": "error",
    "security/detect-unsafe-regex": "error",
  },
  settings: {
    "import/resolver": {
      typescript: {
        alwaysTryTypes: true,
        project: "./tsconfig.json",
      },
    },
  },
};
```

## 🎨 Prettier Configuration

```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "all",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "endOfLine": "lf",
  "arrowParens": "avoid",
  "bracketSpacing": true,
  "bracketSameLine": false,
  "quoteProps": "as-needed"
}
```

## 🧪 Jest Configuration

```javascript
// jest.config.js (Root)
module.exports = {
  projects: [
    "<rootDir>/apps/*/jest.config.js",
    "<rootDir>/services/*/jest.config.js",
    "<rootDir>/libs/*/jest.config.js",
  ],
  collectCoverageFrom: [
    "apps/**/*.{ts,tsx}",
    "services/**/*.{ts,tsx}",
    "libs/**/*.{ts,tsx}",
    "!**/*.d.ts",
    "!**/*.spec.ts",
    "!**/*.test.ts",
    "!**/node_modules/**",
    "!**/dist/**",
  ],
  coverageDirectory: "<rootDir>/coverage",
  coverageReporters: ["json", "lcov", "text", "clover", "html"],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

```javascript
// services/user-service/jest.config.js
module.exports = {
  displayName: "user-service",
  preset: "ts-jest",
  testEnvironment: "node",
  rootDir: ".",
  testMatch: ["<rootDir>/src/**/*.spec.ts", "<rootDir>/src/**/*.test.ts"],
  collectCoverageFrom: [
    "src/**/*.ts",
    "!src/**/*.spec.ts",
    "!src/**/*.test.ts",
    "!src/main.ts",
    "!src/**/*.interface.ts",
    "!src/**/*.dto.ts",
  ],
  coverageDirectory: "<rootDir>/coverage",
  moduleNameMapping: {
    "^@/(.*)$": "<rootDir>/src/$1",
    "^@domain/(.*)$": "<rootDir>/src/domain/$1",
    "^@application/(.*)$": "<rootDir>/src/application/$1",
    "^@infrastructure/(.*)$": "<rootDir>/src/infrastructure/$1",
    "^@interface/(.*)$": "<rootDir>/src/interface/$1",
  },
  setupFilesAfterEnv: ["<rootDir>/src/tests/setup.ts"],
  testTimeout: 30000,
  verbose: true,
};
```

## 🐳 Docker Configuration

```dockerfile
# Dockerfile.dev (Development)
FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
COPY lerna.json ./
COPY tsconfig*.json ./

# Copy workspace packages
COPY apps/ ./apps/
COPY services/ ./services/
COPY libs/ ./libs/

RUN npm ci

# Development command
CMD ["npm", "run", "dev"]
```

```yaml
# docker-compose.yml (Development)
version: "3.8"

services:
  # Databases
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: enterprise_platform
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/database/postgresql/init:/docker-entrypoint-initdb.d

  mongodb:
    image: mongo:6-alpine
    environment:
      MONGO_INITDB_ROOT_USERNAME: dev_user
      MONGO_INITDB_ROOT_PASSWORD: dev_password
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage

  # Message Brokers
  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  # Monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources

  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "14268:14268"
      - "16686:16686"
    environment:
      - COLLECTOR_OTLP_ENABLED=true

  # Services
  api-gateway:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: api-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=************************************************/enterprise_platform
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
      - kafka
    volumes:
      - .:/app
      - /app/node_modules

  user-service:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: user-service
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=************************************************/enterprise_platform
      - REDIS_URL=redis://redis:6379
      - KAFKA_BROKERS=kafka:9092
    depends_on:
      - postgres
      - redis
      - kafka
    volumes:
      - .:/app
      - /app/node_modules

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  qdrant_data:
  prometheus_data:
  grafana_data:
```

## 🔒 Environment Configuration

```bash
# .env.example
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
MONGODB_URL=*********************************************************
REDIS_URL=redis://localhost:6379

# Authentication & Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret
ENCRYPTION_KEY=your-32-character-encryption-key

# External Services
EMAIL_SERVICE_API_KEY=your-email-service-api-key
STORAGE_SERVICE_ACCESS_KEY=your-storage-access-key
STORAGE_SERVICE_SECRET_KEY=your-storage-secret-key

# Message Brokers
KAFKA_BROKERS=localhost:9092
RABBITMQ_URL=amqp://localhost:5672

# Monitoring & Observability
PROMETHEUS_ENDPOINT=http://localhost:9090
JAEGER_ENDPOINT=http://localhost:14268/api/traces
LOG_LEVEL=info

# AI/ML Services
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key
VECTOR_DB_URL=http://localhost:6333

# Feature Flags
FEATURE_AI_RECOMMENDATIONS=true
FEATURE_ADVANCED_ANALYTICS=false
FEATURE_REAL_TIME_NOTIFICATIONS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_METHODS=GET,HEAD,PUT,PATCH,POST,DELETE
CORS_CREDENTIALS=true

# Application Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1
BASE_URL=http://localhost:3000
```

## 📊 Monitoring Configuration

```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alerts/*.yml"

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "api-gateway"
    static_configs:
      - targets: ["api-gateway:3000"]
    metrics_path: /metrics
    scrape_interval: 5s

  - job_name: "user-service"
    static_configs:
      - targets: ["user-service:3000"]
    metrics_path: /metrics
    scrape_interval: 5s

  - job_name: "node-exporter"
    static_configs:
      - targets: ["node-exporter:9100"]

alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093
```

This comprehensive configuration provides a production-ready foundation that implements all the architectural principles and best practices from the knowledge rules.
